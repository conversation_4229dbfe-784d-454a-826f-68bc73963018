import { FastifyRequest, FastifyReply } from 'fastify';
import { TestimonialDTOTypes } from './testimonials.dto';
import { serializeTestimonial } from '@/common/utils/serialize';

import testimonialsService from './testimonials.service';

class TestimonialsController {
  async createTestimonial(request: FastifyRequest, reply: FastifyReply) {
    const { image, ...data } = request.body as TestimonialDTOTypes['CreateTestimonial'];

    // Validate that either videoUrl or image is provided
    if (!data.videoUrl && !image) {
      throw request.server.errors.BAD_REQUEST;
    }

    const imageKey = image ? await request.server.uploadImage(image) : undefined;
    const testimonial = await testimonialsService.create({ ...data, imageKey });
    reply.sendJson(serializeTestimonial(testimonial));
  }

  async getTestimonialById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as TestimonialDTOTypes['GetTestimonial'];
    const testimonial = await testimonialsService.getById(id);
    if (!testimonial) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeTestimonial(testimonial));
  }

  async getTestimonials(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search } = request.query as TestimonialDTOTypes['GetTestimonials'];
    const { testimonials, pagination } = await testimonialsService.getList({
      limit,
      page,
      search,
    });
    reply.sendJson(
      testimonials.map((testimonial) => serializeTestimonial(testimonial)),
      pagination,
    );
  }

  async updateTestimonial(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as TestimonialDTOTypes['UpdateTestimonialParams'];
    const { image, ...data } = request.body as TestimonialDTOTypes['UpdateTestimonialBody'];

    const oldTestimonial = await testimonialsService.getById(id);
    if (!oldTestimonial) throw request.server.errors.NOT_FOUND;

    // Validate that either videoUrl or imageKey will exist after update
    const willHaveVideoUrl = data.videoUrl !== undefined ? data.videoUrl : oldTestimonial.videoUrl;
    const willHaveImageKey = image ? true : oldTestimonial.imageKey;

    if (!willHaveVideoUrl && !willHaveImageKey) {
      throw request.server.errors.BAD_REQUEST;
    }

    const imageKey = image ? await request.server.uploadImage(image) : undefined;
    const testimonial = await testimonialsService.updateById(id, { ...data, imageKey });

    reply.sendJson(serializeTestimonial(testimonial));
  }

  async deleteTestimonial(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as TestimonialDTOTypes['DeleteTestimonial'];
    const testimonial = await testimonialsService.getById(id);
    if (!testimonial) throw request.server.errors.NOT_FOUND;
    await testimonialsService.deleteById(id);
    reply.sendJson();
  }
}

export default new TestimonialsController();
