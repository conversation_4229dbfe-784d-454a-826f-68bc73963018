import quizzesController from './quizzes.controller';

import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { QuizzesDTO } from './quizzes.dto';
import { ModelSchemas } from '@/schema/model.schema';

export default async function quizzesRoutes(app: FastifyInstance) {
  app.get('/quizzes', {
    handler: quizzesController.getQuizzes,
    schema: {
      summary: 'Get quizzes',
      description: 'Get quizzes',
      tags: ['quiz'],
      security: [],
      querystring: QuizzesDTO.GetQuizzesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.QuizSchema), true),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/quizzes/:id', {
    handler: quizzesController.getQuizById,
    schema: {
      summary: 'Get quiz by id',
      description: 'Get quiz by id',
      tags: ['quiz'],
      security: [],
      params: QuizzesDTO.GetQuizSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.QuizSchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/quizzes/weeks', {
    handler: quizzesController.getWeeks,
    schema: {
      summary: 'Get quiz weeks',
      description: 'Get quiz weeks',
      tags: ['quiz'],
      security: [],
      response: {
        200: createSuccessSchema(Type.Array(Type.Number())),
        500: createErrorSchema(500),
      },
    },
  });

  // Admin

  app.get('/admin/quizzes', {
    handler: quizzesController.getQuizzes,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get quizzes',
      description: 'Get quizzes',
      tags: ['quiz', 'admin'],
      querystring: QuizzesDTO.GetQuizzesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.QuizSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/quizzes/:id', {
    handler: quizzesController.getQuizById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get quiz by id',
      description: 'Get quiz by id',
      tags: ['quiz', 'admin'],
      params: QuizzesDTO.GetQuizSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.QuizSchema),
        400: createErrorSchema(),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/quizzes', {
    handler: quizzesController.createQuiz,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create quiz',
      description: 'Create quiz',
      consumes: ['multipart/form-data'],
      tags: ['quiz', 'admin'],
      body: QuizzesDTO.CreateQuizSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.QuizSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.patch('/admin/quizzes/:id', {
    handler: quizzesController.updateQuiz,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update quiz information',
      description: 'Update quiz information',
      consumes: ['multipart/form-data'],
      tags: ['quiz', 'admin'],
      params: QuizzesDTO.UpdateQuizParamsSchema,
      body: QuizzesDTO.UpdateQuizBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.QuizSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/quizzes/:id', {
    handler: quizzesController.deleteQuiz,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete quiz',
      description: 'Delete quiz',
      tags: ['quiz', 'admin'],
      params: QuizzesDTO.DeleteQuizSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
