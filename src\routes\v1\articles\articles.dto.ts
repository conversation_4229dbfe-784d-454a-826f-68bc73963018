import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Type } from '@sinclair/typebox';

const CreateArticleSchema = Type.Object({
  title: Type.String({ minLength: 1 }),
  summary: Type.String({ minLength: 1 }),
  content: Type.Optional(Type.String({ minLength: 1 })),
  thumbnail: Type.Any({ isFile: true }),
  redirectUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
  categoryId: Type.Number(),
  tags: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
});

const UploadImageSchema = Type.Object({
  image: Type.Any({ isFile: true }),
});

const UpdateArticleParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateArticleBodySchema = Type.Object({
  title: Type.String({ minLength: 1 }),
  content: Type.String({ minLength: 1 }),
  summary: Type.Optional(Type.String({ minLength: 1 })),
  thumbnail: Type.Optional(Type.Any({ isFile: true })),
  redirectUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
  categoryId: Type.Optional(Type.Number()),
  tags: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
});

const GetArticleBySlugSchema = Type.Object({
  slug: Type.String({ minLength: 1 }),
});

const GetArticleByIdSchema = Type.Object({
  id: Type.Number(),
});

const GetArticlesSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  categoryId: Type.Optional(Type.Number()),
});

const DeleteArticleSchema = Type.Object({
  id: Type.Number(),
});

const CreateCategorySchema = Type.Object({
  name: Type.String({ minLength: 1 }),
});

const DeleteCategorySchema = Type.Object({
  id: Type.Number(),
});

const UpdateCategoryParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateCategoryBodySchema = Type.Object({
  name: Type.String({ minLength: 1 }),
});

const LikeArticleSchema = Type.Object({
  id: Type.Number(),
});

const ShareArticleSchema = Type.Object({
  id: Type.Number(),
});

const UnlikeArticleSchema = Type.Object({
  id: Type.Number(),
});

export const ArticlesDTO = {
  UploadImageSchema,
  CreateArticleSchema,
  UpdateArticleParamsSchema,
  UpdateArticleBodySchema,
  GetArticleByIdSchema,
  GetArticleBySlugSchema,
  GetArticlesSchema,
  DeleteArticleSchema,
  CreateCategorySchema,
  DeleteCategorySchema,
  UpdateCategoryParamsSchema,
  UpdateCategoryBodySchema,
  LikeArticleSchema,
  ShareArticleSchema,
  UnlikeArticleSchema,
};

export type ArticleDTOTypes = StaticFromSchema<typeof ArticlesDTO>;
