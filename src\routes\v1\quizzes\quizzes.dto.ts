import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateQuizSchema = Type.Object({
  title: Type.String({ minLength: 1 }),
  week: Type.Number(),
  description: Type.String({ minLength: 1 }),
  kahootUrl: Type.String({ format: 'uri', minLength: 1 }),
  tags: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  image: Type.Optional(Type.Any({ isFile: true })),
});

const UpdateQuizParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateQuizBodySchema = Type.Object({
  title: Type.Optional(Type.String({ minLength: 1 })),
  week: Type.Optional(Type.Number()),
  description: Type.Optional(Type.String({ minLength: 1 })),
  kahootUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
  tags: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  image: Type.Optional(Type.Any({ isFile: true })),
});

const GetQuizSchema = Type.Object({
  id: Type.Number(),
});

const GetQuizzesSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  tag: Type.Optional(Type.String({ minLength: 1 })),
  week: Type.Optional(Type.Number()),
});

const DeleteQuizSchema = Type.Object({
  id: Type.Number(),
});

export const QuizzesDTO = {
  CreateQuizSchema,
  UpdateQuizParamsSchema,
  UpdateQuizBodySchema,
  GetQuizSchema,
  GetQuizzesSchema,
  DeleteQuizSchema,
};

export type QuizDTOTypes = StaticFromSchema<typeof QuizzesDTO>;
