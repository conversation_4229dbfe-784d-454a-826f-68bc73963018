import { FastifyRequest, FastifyReply } from 'fastify';
import { CompanyDTOTypes } from './companies.dto';
import { serializeCompany } from '@/common/utils/serialize';

import companiesService from './companies.service';

class CompaniesController {
  async createCompany(request: FastifyRequest, reply: FastifyReply) {
    const { employerName, employerEmail, employerPassword, logo, ...data } =
      request.body as CompanyDTOTypes['CreateCompany'];

    const logoKey = logo ? await request.server.uploadImage(logo) : undefined;
    const company = await companiesService.create(
      { ...data, logoKey },
      { name: employerName, email: employerEmail, password: employerPassword },
    );

    reply.sendJson(serializeCompany(company));
  }

  async getCompanyById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as CompanyDTOTypes['GetCompany'];
    const company = await companiesService.getById(id);
    if (!company) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeCompany(company));
  }

  async getCompanies(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search, companyTypeId } = request.query as CompanyDTOTypes['GetCompanies'];
    const { companies, pagination } = await companiesService.getList({
      limit,
      page,
      search,
      companyTypeId,
    });
    reply.sendJson(
      companies.map((company) => serializeCompany(company)),
      pagination,
    );
  }

  async updateCompany(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as CompanyDTOTypes['UpdateCompanyParams'];
    const { logo, ...data } = request.body as CompanyDTOTypes['UpdateCompanyBody'];

    const oldCompany = await companiesService.getById(id);
    if (!oldCompany) throw request.server.errors.NOT_FOUND;

    const logoKey = logo ? await request.server.uploadImage(logo) : undefined;
    const company = await companiesService.updateById(id, { ...data, logoKey });

    reply.sendJson(serializeCompany(company));
  }

  async deleteCompany(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as CompanyDTOTypes['DeleteCompany'];
    const company = await companiesService.getById(id);
    if (!company) throw request.server.errors.NOT_FOUND;
    await companiesService.deleteById(id);
    reply.sendJson();
  }

  async getCompanyTypes(request: FastifyRequest, reply: FastifyReply) {
    const companyTypes = await companiesService.getCompanyTypes();
    reply.sendJson(companyTypes);
  }

  async createCompanyType(request: FastifyRequest, reply: FastifyReply) {
    const { name } = request.body as CompanyDTOTypes['CreateCompanyType'];
    const companyType = await companiesService.createCompanyType(name);
    reply.sendJson(companyType);
  }

  async updateCompanyType(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as CompanyDTOTypes['UpdateCompanyTypeParams'];
    const { name } = request.body as CompanyDTOTypes['UpdateCompanyTypeBody'];
    const oldCompanyType = await companiesService.getCompanyTypeById(id);
    if (!oldCompanyType) throw request.server.errors.NOT_FOUND;
    const companyType = await companiesService.updateCompanyTypeById(id, name);
    reply.sendJson(companyType);
  }

  async deleteCompanyType(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as CompanyDTOTypes['DeleteCompanyType'];
    const companyType = await companiesService.getCompanyTypeById(id);
    if (!companyType) throw request.server.errors.NOT_FOUND;
    await companiesService.deleteCompanyTypeById(id);
    reply.sendJson();
  }
}

export default new CompaniesController();
