import { FastifyRequest, FastifyReply } from 'fastify';
import { ContactDTOTypes } from './contact.dto';

import contactService from './contact.service';

class ContactController {
  async createContactMessage(request: FastifyRequest, reply: FastifyReply) {
    const { name, phone, email, address, message } =
      request.body as ContactDTOTypes['CreateContactMessage'];

    const contactMessage = await contactService.create({
      name,
      phone,
      email,
      address,
      message,
    });

    reply.sendJson(contactMessage);
  }

  async getContactMessageById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ContactDTOTypes['GetContactMessageById'];
    const contactMessage = await contactService.getById(id);
    if (!contactMessage) throw request.server.errors.NOT_FOUND;
    reply.sendJson(contactMessage);
  }

  async getContactMessages(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search } = request.query as ContactDTOTypes['GetContactMessages'];
    const { contactMessages, pagination } = await contactService.getList({
      limit,
      page,
      search,
    });

    reply.sendJson(contactMessages, pagination);
  }

  async updateContactMessage(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ContactDTOTypes['UpdateContactMessageParams'];
    const updateData = request.body as ContactDTOTypes['UpdateContactMessageBody'];

    const existingContactMessage = await contactService.getById(id);
    if (!existingContactMessage) throw request.server.errors.NOT_FOUND;

    const contactMessage = await contactService.updateById(id, updateData);
    reply.sendJson(contactMessage);
  }

  async deleteContactMessage(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as ContactDTOTypes['DeleteContactMessage'];

    const existingContactMessage = await contactService.getById(id);
    if (!existingContactMessage) throw request.server.errors.NOT_FOUND;

    await contactService.deleteById(id);
    reply.sendJson();
  }
}

export default new ContactController();
