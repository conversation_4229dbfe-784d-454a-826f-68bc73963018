import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma } from '@prisma/client';

type TestimonialServiceOptions = {
  omit?: Prisma.TestimonialOmit;
};

class TestimonialsService {
  async create(data: {
    name: string;
    title: string;
    content: string;
    videoUrl?: string;
    imageKey?: string;
  }) {
    return await withTransaction(async (tx) => {
      return await tx.testimonial.create({ data });
    });
  }

  async updateById(
    id: number,
    data: {
      name?: string;
      title?: string;
      content?: string;
      videoUrl?: string;
      imageKey?: string;
    },
  ) {
    return await withTransaction(async (tx) => {
      return await tx.testimonial.update({
        where: { id },
        data,
      });
    });
  }

  async getById(id: number, options: TestimonialServiceOptions = {}) {
    const { omit } = options;
    return await prisma.testimonial.findUnique({
      where: { id },
      omit,
    });
  }

  async getList(
    params: {
      limit?: number;
      page?: number;
      search?: string;
    },
    options: TestimonialServiceOptions = {},
  ) {
    const { limit = 10, page = 1, search } = params;
    const { omit } = options;
    const skip = (page - 1) * limit;

    const where: Prisma.TestimonialWhereInput = {
      deletedAt: null,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { title: { contains: search, mode: 'insensitive' } },
          { content: { contains: search, mode: 'insensitive' } },
        ],
      }),
    };

    const [testimonials, total] = await Promise.all([
      prisma.testimonial.findMany({
        where,
        omit,
        skip,
        take: limit,
      }),
      prisma.testimonial.count({ where }),
    ]);

    return {
      testimonials,
      pagination: {
        total,
        page,
        limit,
      },
    };
  }

  async deleteById(id: number) {
    return await withTransaction(async (tx) => {
      return await tx.testimonial.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    });
  }
}

export default new TestimonialsService();
