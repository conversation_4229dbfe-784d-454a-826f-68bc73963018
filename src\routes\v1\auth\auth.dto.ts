import { PasswordSchema } from '@/schema/global.schema';
import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Type } from '@sinclair/typebox';
import { ModelSchemas } from '@/schema/model.schema';

const AuthResponseSchema = Type.Object({
  accessToken: Type.String(),
  //  refreshToken: Type.String(),
});

const LoginSchema = Type.Object({
  email: Type.String({ format: 'email', minLength: 1 }),
  password: PasswordSchema,
});

const RegisterSchema = Type.Object({
  email: Type.String({ format: 'email', minLength: 1 }),
  password: PasswordSchema,
  givenName: Type.String({ minLength: 1 }),
  familyName: Type.String({ minLength: 1 }),
  gender: ModelSchemas.GenderSchema,
  dob: Type.String({ format: 'date' }),
});

const VerifySchema = Type.Object({
  token: Type.String(),
});

export const AuthDTO = { AuthResponseSchema, LoginSchema, RegisterSchema, VerifySchema };
export type AuthDTOTypes = StaticFromSchema<typeof AuthDTO>;
