import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateTestimonialSchema = Type.Object({
  name: Type.String({ minLength: 1 }),
  title: Type.String({ minLength: 1 }),
  content: Type.String({ minLength: 1 }),
  videoUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
  image: Type.Optional(Type.Any({ isFile: true })),
});

const UpdateTestimonialParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateTestimonialBodySchema = Type.Object({
  name: Type.Optional(Type.String({ minLength: 1 })),
  title: Type.Optional(Type.String({ minLength: 1 })),
  content: Type.Optional(Type.String({ minLength: 1 })),
  videoUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
  image: Type.Optional(Type.Any({ isFile: true })),
});

const GetTestimonialSchema = Type.Object({
  id: Type.Number(),
});

const GetTestimonialsSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
});

const DeleteTestimonialSchema = Type.Object({
  id: Type.Number(),
});

export const TestimonialsDTO = {
  CreateTestimonialSchema,
  UpdateTestimonialParamsSchema,
  UpdateTestimonialBodySchema,
  GetTestimonialSchema,
  GetTestimonialsSchema,
  DeleteTestimonialSchema,
};

export type TestimonialDTOTypes = StaticFromSchema<typeof TestimonialsDTO>;
