import quizzesService from './quizzes.service';

import { FastifyRequest, FastifyReply } from 'fastify';
import { QuizDTOTypes } from './quizzes.dto';
import { serializeQuiz } from '@/common/utils/serialize';

class QuizzesController {
  async createQuiz(request: FastifyRequest, reply: FastifyReply) {
    const { image, ...data } = request.body as QuizDTOTypes['CreateQuiz'];
    const imageKey = image ? await request.server.uploadImage(image) : undefined;
    const quiz = await quizzesService.create({ ...data, imageKey });
    reply.send<PERSON>son(serializeQuiz(quiz));
  }

  async getQuizById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as QuizDTOTypes['GetQuiz'];
    const quiz = await quizzesService.getById(id);
    if (!quiz) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeQuiz(quiz));
  }

  async getQuizzes(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search, tag, week } = request.query as QuizDTOTypes['GetQuizzes'];
    const { quizzes, pagination } = await quizzesService.getList({
      limit,
      page,
      search,
      tag,
      week,
    });
    reply.sendJson(
      quizzes.map((quiz) => serializeQuiz(quiz)),
      pagination,
    );
  }

  async updateQuiz(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as QuizDTOTypes['UpdateQuizParams'];
    const { image, ...data } = request.body as QuizDTOTypes['UpdateQuizBody'];

    const oldQuiz = await quizzesService.getById(id);
    if (!oldQuiz) throw request.server.errors.NOT_FOUND;

    const imageKey = image ? await request.server.uploadImage(image) : undefined;
    const quiz = await quizzesService.updateById(id, { ...data, imageKey });

    reply.sendJson(serializeQuiz(quiz));
  }

  async deleteQuiz(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as QuizDTOTypes['DeleteQuiz'];
    const quiz = await quizzesService.getById(id);
    if (!quiz) throw request.server.errors.NOT_FOUND;
    await quizzesService.deleteById(id);
    reply.sendJson();
  }

  async getWeeks(request: FastifyRequest, reply: FastifyReply) {
    const weeks = await quizzesService.getWeeks();
    reply.sendJson(weeks.map((week) => week.week));
  }
}

export default new QuizzesController();
