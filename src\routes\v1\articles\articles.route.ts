import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { ArticlesDTO } from './articles.dto';
import { ModelSchemas } from '@/schema/model.schema';

import articlesController from './articles.controller';

export default async function articlesRoutes(app: FastifyInstance) {
  app.get('/articles', {
    handler: articlesController.getArticles,
    schema: {
      summary: 'Get list articles',
      description: 'Get list articles',
      tags: ['articles'],
      security: [],
      querystring: ArticlesDTO.GetArticlesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.ArticleSchema), true),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/articles/:slug', {
    handler: articlesController.getArticleBySlug,
    schema: {
      summary: 'Get articles by slug',
      description: 'Get articles by slug',
      tags: ['articles'],
      security: [],
      params: ArticlesDTO.GetArticleBySlugSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/articles/categories', {
    handler: articlesController.getCategories,
    schema: {
      summary: 'Get categories',
      description: 'Get categories',
      tags: ['articles'],
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.CategorySchema)),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/articles/like/:id', {
    handler: articlesController.likeArticle,
    schema: {
      summary: 'Like article',
      description: 'Like article',
      tags: ['articles'],
      security: [],
      params: ArticlesDTO.LikeArticleSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/articles/unlike/:id', {
    handler: articlesController.likeArticle,
    schema: {
      summary: 'Unlike article',
      description: 'Unlike article',
      tags: ['articles'],
      security: [],
      params: ArticlesDTO.UnlikeArticleSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/articles/share/:id', {
    handler: articlesController.shareArticle,
    schema: {
      summary: 'Share article',
      description: 'Share article',
      tags: ['articles'],
      security: [],
      params: ArticlesDTO.ShareArticleSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/articles/trending', {
    handler: articlesController.getTrendingArticles,
    schema: {
      summary: 'Get trending articles',
      description: 'Get trending articles',
      tags: ['articles'],
      security: [],
      response: {
        200: createSuccessSchema(ModelSchemas.ArticleSchema),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  // Admin

  app.get('/admin/articles', {
    handler: articlesController.getArticles,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get list articles',
      description: 'Get list articles',
      tags: ['articles', 'admin'],
      querystring: ArticlesDTO.GetArticlesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.ArticleSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/articles/upload', {
    handler: articlesController.uploadImage,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Upload image',
      description: 'Upload image',
      tags: ['articles', 'admin'],
      consumes: ['multipart/form-data'],
      body: ArticlesDTO.UploadImageSchema,
      response: {
        200: createSuccessSchema(Type.Object({ imageUrl: Type.String({ format: 'uri' }) })),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.patch('/admin/articles/:id', {
    handler: articlesController.updateArticle,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update articles',
      description: 'Update articles',
      tags: ['articles', 'admin'],
      params: ArticlesDTO.UpdateArticleParamsSchema,
      body: ArticlesDTO.UpdateArticleBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/articles/:id', {
    handler: articlesController.getArticleById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get articles by ID',
      description: 'Get articles by ID',
      tags: ['articles', 'admin'],
      params: ArticlesDTO.GetArticleByIdSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/articles', {
    handler: articlesController.createArticle,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create articles',
      description: 'Create articles',
      tags: ['articles', 'admin'],
      consumes: ['multipart/form-data'],
      body: ArticlesDTO.CreateArticleSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/articles/:id', {
    handler: articlesController.deleteArticle,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete articles',
      description: 'Delete articles',
      tags: ['articles', 'admin'],
      params: ArticlesDTO.DeleteArticleSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/articles/categories', {
    handler: articlesController.createCategory,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create category',
      description: 'Create category',
      tags: ['articles', 'admin'],
      body: ArticlesDTO.CreateCategorySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.CategorySchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/articles/categories', {
    handler: articlesController.getCategories,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get categories',
      description: 'Get categories',
      tags: ['articles', 'admin'],
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.CategorySchema)),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/articles/categories/:id', {
    handler: articlesController.deleteCategory,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete category',
      description: 'Delete category',
      tags: ['articles', 'admin'],
      params: ArticlesDTO.DeleteCategorySchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.put('/admin/articles/categories/:id', {
    handler: articlesController.updateCategory,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update category',
      description: 'Update category',
      tags: ['articles', 'admin'],
      params: ArticlesDTO.UpdateCategoryParamsSchema,
      body: ArticlesDTO.UpdateCategoryBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.CategorySchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
