import 'fastify';

import { Prisma, PrismaClient, Role } from '@prisma/client';
import { ApiError } from '@/common/classes/ApiError';
import { OAuth2Namespace } from '@fastify/oauth2';
import { Client } from 'minio';
import { FileTypeResult } from 'file-type';
import { MinioBucket } from '@/common/constants/minio';

type Pagination = {
  page: number;
  limit: number;
  total: number;
};

declare module 'fastify' {
  interface FastifyInstance {
    config: {
      NODE_ENV: string;

      API_NAME: string;
      API_PORT: number;
      API_HOST: string;
      API_VERSION: string;
      API_PREFIX: string;
      API_URL: string;

      FRONTEND_URL: string;

      GOOGLE_CLIENT_ID: string;
      GOOGLE_CLIENT_SECRET: string;

      DATABASE_URL: string;

      JWT_ACCESS_SECRET: string;
      JWT_ACCESS_EXPIRATION_TIME: string;
      JWT_REFRESH_SECRET: string;
      JWT_REFRESH_EXPIRATION_TIME: string;

      MINIO_ENDPOINT: string;
      MINIO_PORT: number;
      MINIO_ACCESS_KEY: string;
      MINIO_SECRET_KEY: string;
      MINIO_URL: string;

      VAPID_SUBJECT: string;
      VAPID_PUBLIC_KEY: string;
      VAPID_PRIVATE_KEY: string;

      SMTP_HOST: string;
      SMTP_PORT: number;
      SMTP_USER: string;
      SMTP_PASS: string;
      SMTP_FROM: string;
    };

    prisma: PrismaClient;
    withTransaction: <T>(fn: (tx: Prisma.TransactionClient) => Promise<T>) => Promise<T>;

    minio: Client;
    uploadToMinio: (file: {
      buffer: Buffer;
      bucket: MinioBucket;
      fileType: FileTypeResult;
    }) => Promise<string>;
    uploadImage: (image: Buffer) => Promise<string>;

    webpush: typeof import('web-push');

    transporter: import('nodemailer').Transporter;
    sendVerificationEmail: (to: string, token: string) => Promise<void>;

    googleOAuth2: OAuth2Namespace;
    authenticate: (
      request: import('fastify').FastifyRequest,
      reply: import('fastify').FastifyReply,
    ) => Promise<void>;
    roleAuthenticate: (
      roles: Role[],
    ) => (
      request: import('fastify').FastifyRequest,
      reply: import('fastify').FastifyReply,
    ) => Promise<void>;

    errors: {
      ApiError: typeof ApiError;

      BAD_REQUEST: ApiError;
      UNAUTHORIZED: ApiError;
      FORBIDDEN: ApiError;
      NOT_FOUND: ApiError;

      INTERNAL_SERVER_ERROR: ApiError;

      UNKNOWN_ERROR: ApiError;
      VALIDATION_ERROR: ApiError;
      FILE_TYPE_ERROR: ApiError;

      EMAIL_ALREADY_EXISTS: ApiError;
      INVALID_CREDENTIALS: ApiError;
      INVALID_TOKEN: ApiError;
      ALREADY_VERIFIED: ApiError;
      TOKEN_EXPIRED: ApiError;

      TOO_MANY_REQUESTS: ApiError;
    };
  }

  interface FastifyRequest {
    person: Prisma.PersonGetPayload<{ include: { profile: true; employer: true } }>;
    i18n: { t: (key: string) => string; lang: string };
  }

  interface FastifyReply {
    sendJson(data?: unknown, pagination?: Pagination): void;
  }
}
