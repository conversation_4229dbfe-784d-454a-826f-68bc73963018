import { ApiError } from '../classes/ApiError';

export const HttpErrors = {
  // 4xx errors
  BAD_REQUEST: new ApiError({
    message: 'errors.bad_request',
    statusCode: 400,
    code: 'BAD_REQUEST',
  }),
  UNAUTHORIZED: new ApiError({
    message: 'errors.unauthorized',
    statusCode: 401,
    code: 'UNAUTHORIZED',
  }),
  FORBIDDEN: new ApiError({
    message: 'errors.forbidden',
    statusCode: 403,
    code: 'FORBIDDEN',
  }),
  NOT_FOUND: new ApiError({
    message: 'errors.not_found',
    statusCode: 404,
    code: 'NOT_FOUND',
  }),

  // 5xx errors
  INTERNAL_SERVER_ERROR: new ApiError({
    message: 'errors.internal_server_error',
    statusCode: 500,
    code: 'INTERNAL_SERVER_ERROR',
  }),

  // Global errors
  UNKNOWN_ERROR: new ApiError({
    message: 'errors.unknown_error',
    statusCode: 500,
    customCode: 1000,
    code: 'UNKNOWN_ERROR',
  }),

  // Validation errors
  VALIDATION_ERROR: new ApiError({
    message: 'errors.validation_error',
    statusCode: 400,
    customCode: 1001,
    code: 'VALIDATION_ERROR',
  }),
  FILE_TYPE_ERROR: new ApiError({
    message: 'errors.file_type_error',
    statusCode: 400,
    customCode: 1002,
    code: 'FILE_TYPE_ERROR',
  }),

  // Auth errors
  INVALID_CREDENTIALS: new ApiError({
    message: 'errors.invalid_credentials',
    statusCode: 401,
    customCode: 2001,
    code: 'INVALID_CREDENTIALS',
  }),
  EMAIL_ALREADY_EXISTS: new ApiError({
    message: 'errors.email_already_exists',
    statusCode: 400,
    customCode: 2002,
    code: 'EMAIL_ALREADY_EXISTS',
  }),
  INVALID_TOKEN: new ApiError({
    message: 'errors.invalid_token',
    statusCode: 400,
    customCode: 2003,
    code: 'INVALID_TOKEN',
  }),
  ALREADY_VERIFIED: new ApiError({
    message: 'errors.already_verified',
    statusCode: 400,
    customCode: 2004,
    code: 'ALREADY_VERIFIED',
  }),
  TOKEN_EXPIRED: new ApiError({
    message: 'errors.token_expired',
    statusCode: 400,
    customCode: 2005,
    code: 'TOKEN_EXPIRED',
  }),

  // Rate limit
  TOO_MANY_REQUESTS: new ApiError({
    message: 'errors.too_many_requests',
    statusCode: 429,
    customCode: 3001,
    code: 'TOO_MANY_REQUESTS',
  }),
} as const;
