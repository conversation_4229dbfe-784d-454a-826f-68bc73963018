import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { ContactDTO } from './contact.dto';
import { ModelSchemas } from '@/schema/model.schema';
import contactController from './contact.controller';

export default async function contactRoutes(app: FastifyInstance) {
  // Public
  app.post('/contact', {
    handler: contactController.createContactMessage,
    schema: {
      summary: 'Create contact message',
      description: 'Create a new contact message',
      tags: ['contact'],
      security: [],
      body: ContactDTO.CreateContactMessageSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ContactMessageSchema),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  // Admin
  app.get('/admin/contact', {
    handler: contactController.getContactMessages,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get contact messages',
      description: 'Get list of contact messages (Admin only)',
      tags: ['contact', 'admin'],
      querystring: ContactDTO.GetContactMessagesSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.ContactMessageSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/contact/:id', {
    handler: contactController.getContactMessageById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get contact message by ID',
      description: 'Get a specific contact message by ID (Admin only)',
      tags: ['contact', 'admin'],
      params: ContactDTO.GetContactMessageByIdSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.ContactMessageSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/contact/:id', {
    handler: contactController.deleteContactMessage,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete contact message',
      description: 'Delete a contact message (Admin only)',
      tags: ['contact', 'admin'],
      params: ContactDTO.DeleteContactMessageSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
