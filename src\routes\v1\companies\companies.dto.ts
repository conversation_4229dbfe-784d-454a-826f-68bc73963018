import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema, PasswordSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateCompanySchema = Type.Object({
  name: Type.String({ minLength: 1 }),
  address: Type.String({ minLength: 1 }),
  description: Type.Optional(Type.String({ minLength: 1 })),
  website: Type.Optional(Type.String({ format: 'uri' })),
  logo: Type.Optional(Type.Any({ isFile: true })),
  companyTypeId: Type.Number(),
  videoUrl: Type.Optional(Type.String({ format: 'uri' })),
  services: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  highlights: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  // Person data for creating employer
  employerName: Type.String({ minLength: 1 }),
  employerEmail: Type.String({ format: 'email', minLength: 1 }),
  employerPassword: PasswordSchema,
});

const UpdateCompanyParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateCompanyBodySchema = Type.Object({
  name: Type.Optional(Type.String({ minLength: 1 })),
  address: Type.Optional(Type.String({ minLength: 1 })),
  description: Type.Optional(Type.String({ minLength: 1 })),
  website: Type.Optional(Type.String({ format: 'uri' })),
  logo: Type.Optional(Type.Any({ isFile: true })),
  videoUrl: Type.Optional(Type.String({ format: 'uri' })),
  services: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
  highlights: Type.Optional(Type.Array(Type.String({ minLength: 1 }))),
});

const GetCompanySchema = Type.Object({
  id: Type.Number(),
});

const GetCompaniesSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  companyTypeId: Type.Optional(Type.Number()),
});

const DeleteCompanySchema = Type.Object({
  id: Type.Number(),
});

const CreateCompanyTypeSchema = Type.Object({
  name: Type.String({ minLength: 1 }),
});

const UpdateCompanyTypeParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateCompanyTypeBodySchema = Type.Object({
  name: Type.String({ minLength: 1 }),
});

const DeleteCompanyTypeSchema = Type.Object({
  id: Type.Number(),
});

export const CompaniesDTO = {
  CreateCompanySchema,
  UpdateCompanyParamsSchema,
  UpdateCompanyBodySchema,
  GetCompanySchema,
  GetCompaniesSchema,
  DeleteCompanySchema,
  CreateCompanyTypeSchema,
  UpdateCompanyTypeParamsSchema,
  UpdateCompanyTypeBodySchema,
  DeleteCompanyTypeSchema,
};

export type CompanyDTOTypes = StaticFromSchema<typeof CompaniesDTO>;
