import fp from 'fastify-plugin';
import { FastifyInstance } from 'fastify';

const envSchema = {
  type: 'object',
  required: [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'DATABASE_URL',
    'JWT_ACCESS_SECRET',
    'JWT_REFRESH_SECRET',
    'MINIO_ACCESS_KEY',
    'MINIO_SECRET_KEY',
    'VAPID_SUBJECT',
    'VAPID_PUBLIC_KEY',
    'VAPID_PRIVATE_KEY',
    'SMTP_HOST',
    'SMTP_PORT',
    'SMTP_USER',
    'SMTP_PASS',
    'SMTP_FROM',
  ],
  properties: {
    // Environment setting
    NODE_ENV: { type: 'string', default: 'development' },

    // Api setting
    API_NAME: { type: 'string', default: '3000' },
    API_PORT: { type: 'string', default: 'API' },
    API_HOST: { type: 'string', default: '0.0.0.0' },
    API_VERSION: { type: 'string', default: '1.0.0' },
    API_PREFIX: { type: 'string', default: '/api' },
    API_URL: { type: 'string', default: 'http://localhost:3000' },

    // Frontend setting
    FRONTEND_URL: { type: 'string', default: 'http://localhost:3000' },

    // OAuth setting
    GOOGLE_CLIENT_ID: { type: 'string' },
    GOOGLE_CLIENT_SECRET: { type: 'string' },

    // Database setting
    DATABASE_URL: { type: 'string' },

    // JWT setting
    JWT_ACCESS_SECRET: { type: 'string' },
    JWT_ACCESS_EXPIRATION_TIME: { type: 'string', default: '15m' },
    JWT_REFRESH_SECRET: { type: 'string' },
    JWT_REFRESH_EXPIRATION_TIME: { type: 'string', default: '7d' },

    // MINIO setting
    MINIO_ENDPOINT: { type: 'string', default: 'localhost' },
    MINIO_PORT: { type: 'number', default: 9000 },
    MINIO_ACCESS_KEY: { type: 'string' },
    MINIO_SECRET_KEY: { type: 'string' },
    MINIO_URL: { type: 'string', default: 'http://localhost:9000' },

    // Webpush setting
    VAPID_SUBJECT: { type: 'string' },
    VAPID_PUBLIC_KEY: { type: 'string' },
    VAPID_PRIVATE_KEY: { type: 'string' },

    // SMTP setting
    SMTP_HOST: { type: 'string' },
    SMTP_PORT: { type: 'number' },
    SMTP_USER: { type: 'string' },
    SMTP_PASS: { type: 'string' },
    SMTP_FROM: { type: 'string' },
  },
};

export const env: FastifyInstance['config'] = { ...process.env } as any;

const envPlugin = fp(async (app) => {
  await app.register(import('@fastify/env'), { schema: envSchema, dotenv: true });
});

export default envPlugin;
