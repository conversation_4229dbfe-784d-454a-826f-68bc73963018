import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma } from '@prisma/client';

class ContactService {
  async create(data: {
    name: string;
    phone: string;
    email?: string;
    address: string;
    message: string;
  }) {
    return await withTransaction(async (tx) => {
      return await tx.contactUs.create({ data });
    });
  }

  async updateById(
    id: number,
    data: {
      name?: string;
      phone?: string;
      email?: string;
      address?: string;
      message?: string;
    },
  ) {
    return await withTransaction(async (tx) => {
      return await tx.contactUs.update({
        where: { id },
        data,
      });
    });
  }

  async getById(id: number) {
    return await prisma.contactUs.findUnique({
      where: { id },
    });
  }

  async getList(query: { limit?: number; page?: number; search?: string }) {
    const { limit = 10, page = 1, search } = query;

    const where: Prisma.ContactUsWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
        { message: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [contactMessages, count] = await prisma.$transaction([
      prisma.contactUs.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
      }),
      prisma.contactUs.count({ where }),
    ]);

    return { contactMessages, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await withTransaction(async (tx) => {
      return await tx.contactUs.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    });
  }
}

export default new ContactService();
