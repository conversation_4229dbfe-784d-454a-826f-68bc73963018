import schoolsService from './schools.service';

import { FastifyRequest, FastifyReply } from 'fastify';
import { SchoolDTOTypes } from './schools.dto';
import { serializeSchool } from '@/common/utils/serialize';

class SchoolsController {
  async createSchool(request: FastifyRequest, reply: FastifyReply) {
    const { logo, ...schoolData } = request.body as SchoolDTOTypes['CreateSchool'];

    const logoKey = logo ? await request.server.uploadImage(logo) : undefined;
    const school = await schoolsService.create({ ...schoolData, logoKey });

    reply.sendJson(serializeSchool(school));
  }

  async getSchoolById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as SchoolDTOTypes['GetSchool'];
    const school = await schoolsService.getById(id);
    if (!school) throw request.server.errors.NOT_FOUND;
    reply.sendJson(serializeSchool(school));
  }

  async getSchools(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page, search, city, schoolTypeId } =
      request.query as SchoolDTOTypes['GetSchools'];

    const { schools, pagination } = await schoolsService.getList({
      limit,
      page,
      search,
      city,
      schoolTypeId,
    });

    reply.sendJson(
      schools.map((school) => serializeSchool(school)),
      pagination,
    );
  }

  async getSchoolTypes(request: FastifyRequest, reply: FastifyReply) {
    const schoolTypes = await schoolsService.getSchoolTypes();
    reply.sendJson(schoolTypes);
  }

  async updateSchool(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as SchoolDTOTypes['UpdateSchoolParams'];
    const { logo, ...schoolData } = request.body as SchoolDTOTypes['UpdateSchoolBody'];

    const oldSchool = await schoolsService.getById(id);
    if (!oldSchool) throw request.server.errors.NOT_FOUND;

    const logoKey = logo ? await request.server.uploadImage(logo) : undefined;
    const school = await schoolsService.updateById(id, { ...schoolData, logoKey });

    reply.sendJson(serializeSchool(school));
  }

  async deleteSchool(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as SchoolDTOTypes['DeleteSchool'];
    const school = await schoolsService.getById(id);
    if (!school) throw request.server.errors.NOT_FOUND;
    await schoolsService.deleteById(id);
    reply.sendJson();
  }

  async getCities(request: FastifyRequest, reply: FastifyReply) {
    const cities = await schoolsService.getCities();
    const seen = new Set();

    reply.sendJson([
      cities
        .map((item) => item.city)
        .filter((item) => item !== null)
        .map((city) =>
          city
            .toLowerCase()
            .replace(/\s+/g, ' ') // bỏ khoảng trắng thừa
            .trim()
            .replace(/\b\p{L}/gu, (c) => c.toUpperCase()),
        )
        .filter((item) => {
          const lower = item.toLowerCase();
          if (seen.has(lower)) return false;
          seen.add(lower);
          return true;
        }),
    ]);
  }
}

export default new SchoolsController();
