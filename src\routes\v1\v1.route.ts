import { FastifyInstance } from 'fastify';

import authRoutes from './auth/auth.route';
import articlesRoutes from './articles/articles.route';
import usersRoutes from './users/users.route';
import quizzesRoutes from './quizzes/quizzes.route';
import schoolsRoutes from './schools/schools.route';
import companiesRoutes from './companies/companies.route';
import jobsRoutes from './jobs/jobs.route';
import applicationsRoutes from './applications/applications.route';
import challengesRoutes from './challenge/challenges.route';
import notificationsRoutes from './notifications/notifications.route';
import emailRoutes from './email/email.route';
import contactRoutes from './contact/contact.route';
import testimonialsRoutes from './testimonials/testimonials.route';

export default async function v1Routes(app: FastifyInstance) {
  await app.register(authRoutes);
  await app.register(usersRoutes);
  await app.register(quizzesRoutes);
  await app.register(schoolsRoutes);
  await app.register(companiesRoutes);
  await app.register(jobsRoutes);
  await app.register(applicationsRoutes);
  await app.register(articlesRoutes);
  await app.register(challengesRoutes);
  await app.register(notificationsRoutes);
  await app.register(emailRoutes);
  await app.register(contactRoutes);
  await app.register(testimonialsRoutes);
}
