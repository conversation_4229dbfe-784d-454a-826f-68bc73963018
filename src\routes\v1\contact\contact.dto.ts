import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, PhoneSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateContactMessageSchema = Type.Object({
  name: Type.String({ minLength: 1 }),
  phone: PhoneSchema,
  email: Type.Optional(Type.String({ format: 'email', minLength: 1 })),
  address: Type.String({ minLength: 1 }),
  message: Type.String({ minLength: 1 }),
});

const GetContactMessageByIdSchema = Type.Object({
  id: Type.Number(),
});

const GetContactMessagesSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
});

const UpdateContactMessageParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateContactMessageBodySchema = Type.Object({
  name: Type.Optional(Type.String({ minLength: 1 })),
  phone: Type.Optional(Type.String({ minLength: 1 })),
  email: Type.Optional(Type.String({ format: 'email', minLength: 1 })),
  address: Type.Optional(Type.String({ minLength: 1 })),
  message: Type.Optional(Type.String({ minLength: 1 })),
});

const DeleteContactMessageSchema = Type.Object({
  id: Type.Number(),
});

export const ContactDTO = {
  CreateContactMessageSchema,
  GetContactMessageByIdSchema,
  GetContactMessagesSchema,
  UpdateContactMessageParamsSchema,
  UpdateContactMessageBodySchema,
  DeleteContactMessageSchema,
};

export type ContactDTOTypes = StaticFromSchema<typeof ContactDTO>;
