import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma, Role } from '@prisma/client';
import bcrypt from 'bcryptjs';

type CompanyServiceOptions = {
  include?: Prisma.CompanyInclude;
  omit?: Prisma.CompanyOmit;
};

const defaultInclude: Prisma.CompanyInclude = {
  companyType: true,
  employers: { include: { person: true } },
};

class CompanyService {
  async create(
    data: {
      name: string;
      address: string;
      description?: string;
      website?: string;
      logoKey?: string;
      videoUrl?: string;
      services?: string[];
      highlights?: string[];
      companyTypeId: number;
    },
    employerData: {
      name: string;
      email: string;
      password: string;
    },
    options: CompanyServiceOptions = {},
  ) {
    const { include = defaultInclude, omit } = options;
    const hashedPassword = await bcrypt.hash(employerData.password, 10);

    return await withTransaction(async (tx) => {
      const company = await tx.company.create({ include, omit, data: { ...data } });

      const person = await tx.person.create({
        data: {
          name: employerData.name,
          email: employerData.email,
          password: hashedPassword,
          role: Role.EMPLOYER,
          profile: { create: {} },
        },
      });

      await tx.employer.create({ data: { personId: person.id, companyId: company.id } });
      return company;
    });
  }

  async updateById(
    id: number,
    data: {
      name?: string;
      address?: string;
      description?: string;
      website?: string;
      logoKey?: string;
      videoUrl?: string;
      services?: string[];
      highlights?: string[];
    },
    options: { include?: Prisma.CompanyInclude } = {},
  ) {
    const { include = defaultInclude } = options;

    return await withTransaction(async (tx) => {
      return await tx.company.update({
        include,
        where: { id },
        data: { ...data },
      });
    });
  }

  async getById(id: number, options: CompanyServiceOptions = {}) {
    const { include = defaultInclude } = options;
    return await prisma.company.findUnique({ where: { id }, include });
  }

  async getList(
    query: {
      limit?: number;
      page?: number;
      search?: string;
      companyTypeId?: number;
    },
    options: CompanyServiceOptions = {},
  ) {
    const { limit = 10, page = 1, search, companyTypeId } = query;
    const { include = defaultInclude } = options;

    const where: Prisma.CompanyWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (companyTypeId) {
      where.companyTypeId = companyTypeId;
    }

    const [companies, count] = await prisma.$transaction([
      prisma.company.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
        include,
      }),
      prisma.company.count({ where }),
    ]);

    return { companies, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.company.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }

  async getCompanyTypes() {
    return await prisma.companyType.findMany();
  }

  async getCompanyTypeById(id: number) {
    return await prisma.companyType.findUnique({ where: { id } });
  }

  async createCompanyType(name: string) {
    return await prisma.companyType.create({ data: { name } });
  }

  async updateCompanyTypeById(id: number, name: string) {
    return await prisma.companyType.update({ where: { id }, data: { name } });
  }

  async deleteCompanyTypeById(id: number) {
    return await prisma.companyType.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new CompanyService();
