import bcrypt from 'bcryptjs';

import { prisma } from '@/plugins/prisma.plugin';
import { Gender, Person, Prisma, Role } from '@prisma/client';

type PersonData = {
  email: string;
  name: string;
  password: string;
  role?: Role;
  refSource?: string;
};

type ProfileData = {
  avatarKey?: string;
  address?: string;
  dob?: Date;
  gender?: Gender;
  resumeKey?: string;
  //
  givenName?: string;
  familyName?: string;
  phone?: string;
  city?: string;
  country?: string;
  expertiseDomains?: string[];
  frenchLevel?: string;
  otherLanguages?: string[];
  experience?: string;
  education?: string;
  livedFrance?: string;
  jobAvailability?: string;
  geoMobility?: string;
};

type UserServiceOptions = {
  include?: Prisma.PersonInclude;
  omit?: Prisma.PersonOmit;
  prismaSession?: Prisma.TransactionClient;
};

const defaultInclude: Prisma.PersonInclude = { profile: true, employer: true };

class UsersService {
  async create(data: PersonData, profileData: ProfileData = {}, options: UserServiceOptions = {}) {
    const { password, role = Role.USER, ...personData } = data;
    const { include = defaultInclude, omit, prismaSession = prisma } = options;

    const hashedPassword = await bcrypt.hash(password, 10);

    return await prismaSession.person.create({
      include,
      omit,
      data: {
        ...personData,
        password: hashedPassword,
        role,
        profile: {
          create: {
            ...profileData,
          },
        },
      },
    });
  }

  async comparePassword(person: Person, password: string) {
    if (!person.password || person.password === '') return false;
    return await bcrypt.compare(password, person.password);
  }

  async getByEmail(email: string, options: UserServiceOptions = {}) {
    const { include, omit } = options;
    return await prisma.person.findUnique({ where: { email }, include, omit });
  }

  async getById(id: number, options: UserServiceOptions = {}) {
    const {
      include = { profile: true, employer: { include: { company: true } } },
      omit = { password: true },
    } = options;
    return await prisma.person.findUnique({ where: { id }, include, omit });
  }

  async getList(
    query: { limit?: number; page?: number; role?: string; search?: string },
    options: UserServiceOptions = {},
  ) {
    const { limit = 10, page = 1, role, search } = query;
    const {
      include = { profile: true, employer: { include: { company: true } } },
      omit = { password: true },
    } = options;

    const where: Prisma.PersonWhereInput = {};

    if (role) where.role = role as Role;
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [persons, count] = await prisma.$transaction([
      prisma.person.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
        include,
        omit,
      }),
      prisma.person.count({ where }),
    ]);

    return { persons, pagination: { total: count, page, limit } };
  }

  async updateProfileById(
    id: number,
    profileData: ProfileData = {},
    options: UserServiceOptions = {},
  ) {
    const {
      include = { profile: true, employer: { include: { company: true } } },
      omit = { password: true },
    } = options;
    return await prisma.person.update({
      include,
      omit,
      where: { id },
      data: {
        profile: {
          update: {
            ...profileData,
          },
        },
      },
    });
  }

  async updatePassword(id: number, password: string) {
    const hashedPassword = await bcrypt.hash(password, 10);
    return await prisma.person.update({
      where: { id },
      data: { password: hashedPassword },
    });
  }

  async updateActiveById(id: number, active: boolean) {
    return await prisma.person.update({
      where: { id },
      data: { active },
    });
  }

  async deleteById(id: number) {
    return await prisma.person.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new UsersService();
