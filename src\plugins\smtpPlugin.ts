import fp from 'fastify-plugin';
import nodemailer from 'nodemailer';
import { env } from './env.plugin';

const transporter = nodemailer.createTransport({
  host: env.SMTP_HOST,
  port: env.SMTP_PORT,
  auth: {
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
  },
});

const sendVerificationEmail = async (to: string, token: string) => {
  const subject = '🔐 Xác thực tài khoản của bạn';
  const hostOnly = env.FRONTEND_URL.replace(/^https?:\/\//, '');
  const logoUrl = `${env.FRONTEND_URL}/logo.png`;
  const supportEmail = `support@${hostOnly}`;

  const html = `
    <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; border: 1px solid #eee; padding: 24px; border-radius: 8px;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="${logoUrl}" alt="Logo" style="height: 50px;">
      </div>

      <h2 style="color: #2c3e50;">Xin chào,</h2>
      <p>Chúng tôi đã nhận được yêu cầu xác thực tài khoản của bạn.</p>
      <p>Vui lòng nhấn vào nút bên dưới để xác thực tài khoản:</p>

      <div style="text-align: center; margin: 24px 0;">
        <a href="${env.FRONTEND_URL}/verify?token=${token}"
           style="display: inline-block; background-color: #007bff; color: #fff; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
          Xác thực tài khoản
        </a>
      </div>

      <p>Nếu bạn không yêu cầu điều này, vui lòng bỏ qua email này.</p>

      <p>Trân trọng,</p>
      <p style="font-weight: bold; margin-bottom: 4px;">Đội ngũ Hỗ trợ</p>
      <p style="font-size: 14px; color: #555;">Email: ${supportEmail} | Website: <a href="${env.FRONTEND_URL}" style="color: #007bff;">${hostOnly}</a></p>

      <hr style="margin: 32px 0; border: none; border-top: 1px solid #ddd;">

      <p style="font-size: 13px; color: #888;">Nếu nút không hoạt động, bạn có thể sao chép và dán đường link sau vào trình duyệt:</p>
      <p style="font-size: 13px; word-break: break-all;">
        <a href="${env.FRONTEND_URL}/verify?token=${token}" style="color: #007bff;">${env.FRONTEND_URL}/verify?token=${token}</a>
      </p>

      <div style="text-align: center; margin-top: 32px; font-size: 12px; color: #aaa;">
        © ${new Date().getFullYear()} TiengPhapPlus. All rights reserved.
      </div>
    </div>
  `;

  await transporter.sendMail({ from: env.SMTP_FROM, to, subject, html });
};

const sendCompanyCreatedEmail = async (to: string, password: string) => {
  const subject = 'Tạo tài khoản thành công';

  const html = `
    <p>Xin chào,</p>
    <p>Chúng tôi đã nhận được yêu cầu tạo tài khoản của bạn.</p>
    <p>Thông tin tài khoản của bạn là:</p>
    <p>Email: ${to}</p>
    <p>Mật khẩu: ${password}</p>
    <p>Vui lòng đăng nhập và thay đổi mật khẩu ngay sau khi đăng nhập.</p>
    <p>Để đăng nhập, vui lòng click vào đường link sau:</p>
    <p><a href="${env.API_URL}">Đăng nhập</a></p>
    <p>Trân trọng,</p>
    <p>Đội ngũ hỗ trợ</p>
  `;

  await transporter.sendMail({ from: env.SMTP_FROM, to, subject, html });
};

const smtpPlugin = fp(async (app) => {
  app.decorate('transporter', transporter);
  app.decorate('sendVerificationEmail', sendVerificationEmail);
  app.decorate('sendCompanyCreatedEmail', sendCompanyCreatedEmail);
});

export default smtpPlugin;
