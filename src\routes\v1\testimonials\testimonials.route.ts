import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { TestimonialsDTO } from './testimonials.dto';
import { ModelSchemas } from '@/schema/model.schema';
import testimonialsController from './testimonials.controller';

export default async function testimonialsRoutes(app: FastifyInstance) {
  app.get('/testimonials', {
    handler: testimonialsController.getTestimonials,
    schema: {
      summary: 'Get testimonials',
      description: 'Get testimonials',
      tags: ['testimonial'],
      security: [],
      querystring: TestimonialsDTO.GetTestimonialsSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.TestimonialSchema), true),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/testimonials/:id', {
    handler: testimonialsController.getTestimonialById,
    schema: {
      summary: 'Get testimonial by id',
      description: 'Get testimonial by id',
      tags: ['testimonial'],
      security: [],
      params: TestimonialsDTO.GetTestimonialSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.TestimonialSchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  // Admin

  app.get('/admin/testimonials', {
    handler: testimonialsController.getTestimonials,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get testimonials',
      description: 'Get testimonials',
      tags: ['testimonial', 'admin'],
      querystring: TestimonialsDTO.GetTestimonialsSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.TestimonialSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/admin/testimonials/:id', {
    handler: testimonialsController.getTestimonialById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get testimonial by id',
      description: 'Get testimonial by id',
      tags: ['testimonial', 'admin'],
      params: TestimonialsDTO.GetTestimonialSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.TestimonialSchema),
        400: createErrorSchema(),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/admin/testimonials', {
    handler: testimonialsController.createTestimonial,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create testimonial',
      description: 'Create testimonial',
      consumes: ['multipart/form-data'],
      tags: ['testimonial', 'admin'],
      body: TestimonialsDTO.CreateTestimonialSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.TestimonialSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.patch('/admin/testimonials/:id', {
    handler: testimonialsController.updateTestimonial,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update testimonial information',
      description: 'Update testimonial information',
      consumes: ['multipart/form-data'],
      tags: ['testimonial', 'admin'],
      params: TestimonialsDTO.UpdateTestimonialParamsSchema,
      body: TestimonialsDTO.UpdateTestimonialBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.TestimonialSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.delete('/admin/testimonials/:id', {
    handler: testimonialsController.deleteTestimonial,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete testimonial',
      description: 'Delete testimonial',
      tags: ['testimonial', 'admin'],
      params: TestimonialsDTO.DeleteTestimonialSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
